"use client";

import React from 'react';
import { useModal } from '../contexts/ModalContext';

const Footer = () => {
  const { openModal } = useModal();
  const currentYear = new Date().getFullYear();

  const footerLinks = [
    {
      label: "FAQ",
      action: () => openModal('faq')
    },
    {
      label: "Privacy Policy",
      action: () => openModal('privacy-policy')
    },
    {
      label: "Terms of Service",
      action: () => openModal('terms-of-service')
    }
  ];

  return (
    <footer className="border-t border-secondary/10 pt-8 mt-16">
      <div className="w-3/4 mx-auto px-6">
        {/* Main Footer Content */}
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          {/* Left Side - Legal Links */}
          <div className="flex items-center space-x-6">
            {footerLinks.map((link, index) => (
              <button
                key={index}
                onClick={link.action}
                className="text-secondary/60 hover:text-secondary text-sm transition-colors duration-200 cursor-pointer"
              >
                {link.label}
              </button>
            ))}
          </div>

          {/* Right Side - Copyright */}
          <div className="text-secondary/60 text-sm">
            © {currentYear} Teodor-Cristian Cretu. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
