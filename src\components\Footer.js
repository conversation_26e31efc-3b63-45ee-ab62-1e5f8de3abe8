"use client";

import React from 'react';
import { useModal } from '../contexts/ModalContext';

const Footer = () => {
  const { openModal } = useModal();
  const currentYear = new Date().getFullYear();

  const footerLinks = [
    {
      label: "FAQ",
      action: () => openModal('faq')
    },
    {
      label: "Privacy Policy",
      action: () => openModal('privacy-policy')
    },
    {
      label: "Terms of Service",
      action: () => openModal('terms-of-service')
    }
  ];

  return (
    <footer className="border-t border-secondary/10 pt-8 mt-16">
      <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
        {/* Copyright */}
        <div className="text-secondary/60 text-sm">
          © {currentYear} Teodor-Cristian <PERSON>. All rights reserved.
        </div>

        {/* Legal Links */}
        <div className="flex items-center space-x-6">
          {footerLinks.map((link, index) => (
            <button
              key={index}
              onClick={link.action}
              className="text-secondary/60 hover:text-secondary text-sm transition-colors duration-200 cursor-pointer"
            >
              {link.label}
            </button>
          ))}
        </div>

        {/* Additional Info */}
        <div className="text-secondary/60 text-sm text-center md:text-right">
          <div>Portfolio & Creative Services</div>
          <div className="mt-1">
            <a
              href="mailto:<EMAIL>"
              className="hover:text-secondary transition-colors duration-200"
            >
              <EMAIL>
            </a>
          </div>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="mt-6 pt-6 border-t border-secondary/5">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-2 md:space-y-0">
          <div className="text-secondary/50 text-xs">
            This website uses cookies to enhance user experience and analyze website traffic.
          </div>
          <div className="text-secondary/50 text-xs">
            Built with Next.js, Tailwind CSS, and Framer Motion
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
