"use client";

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import GameConsole from './GameConsole';
import GameScreen from './GameScreen';

const RetroGamesSection = () => {
  const [selectedGame, setSelectedGame] = useState(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Available games
  const games = [
    {
      id: 'snake',
      title: 'Snake',
      description: 'Classic snake game where you eat food to grow longer while avoiding walls and yourself.',
      color: '#4ade80', // Green
      icon: '🐍',
      trivia: 'Originally created in 1976, <PERSON> became famous on Nokia phones in the late 90s. This version is built with React and Canvas API.'
    },
    // Future games will be added here
    // {
    //   id: 'tetris',
    //   title: 'Tetris',
    //   description: 'Block puzzle game where you arrange falling pieces to clear lines.',
    //   color: '#3b82f6', // Blue
    //   icon: '🧩',
    //   trivia: 'Created by <PERSON><PERSON> in 1984, Tetris is one of the most recognizable puzzle games ever made. Coming soon to this arcade!'
    // },
    // {
    //   id: 'duckhunt',
    //   title: 'Duck Hunt',
    //   description: 'Shoot the flying ducks before they escape off screen.',
    //   color: '#f59e0b', // Orange
    //   icon: '🦆',
    //   trivia: 'Released by Nintendo in 1984, Duck Hunt used a light gun accessory. This web version uses mouse clicks instead!'
    // }
  ];

  const handleGameSelect = (game) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(game);
      setIsTransitioning(false);

      // Smooth scroll to center the game area
      setTimeout(() => {
        const gameArea = document.getElementById('game-area');
        if (gameArea) {
          gameArea.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 100); // Small delay to ensure the game screen is rendered
    }, 500); // Transition duration
  };

  const handleBackToConsole = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(null);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <section
      id="retro-games-section"
      className="bg-background min-h-screen flex items-center justify-center relative z-10"
    >
      {/* Container with natural spacing */}
      <div className="w-3/4 mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8" style={{ height: '70vh' }}>
          {/* Game Text - Left Side (1 column) */}
          <div className="lg:col-span-1 flex items-center bg-primary rounded-2xl p-8">
            <div className="w-full">
              <AnimatePresence mode="wait">
                {!selectedGame ? (
                  <motion.div
                    key="console-text"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <h2 className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl mb-8">
                      🎮 Retro Arcade
                    </h2>
                    <p className="text-secondary text-lg mb-8">
                      Thanks for reaching the end! Here's a little treat - some classic games recreated with modern web tech.
                    </p>
                    <div className="text-secondary/60 text-sm">
                      <p>Click on a game card to start playing →</p>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="game-text"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <h2 className="font-heading font-extrabold text-secondary text-3xl lg:text-5xl mb-6">
                      {selectedGame.icon} {selectedGame.title}
                    </h2>
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-heading font-bold text-secondary text-lg mb-2">Game Info</h3>
                        <p className="text-secondary text-sm mb-4">{selectedGame.description}</p>
                      </div>
                      <div>
                        <h3 className="font-heading font-bold text-secondary text-lg mb-2">Controls</h3>
                        <p className="text-secondary/80 text-sm mb-4">
                          Use <span className="bg-secondary/20 px-2 py-1 rounded">WASD</span> or <span className="bg-secondary/20 px-2 py-1 rounded">Arrow Keys</span> to move
                        </p>
                      </div>
                      <div>
                        <h3 className="font-heading font-bold text-secondary text-lg mb-2">Trivia</h3>
                        <p className="text-secondary/80 text-sm">
                          {selectedGame.trivia || "Classic arcade game recreated with modern web technologies for your enjoyment!"}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Game Area - Right Side (2 columns) - Fixed height */}
          <div className="lg:col-span-2 flex items-center justify-center bg-background rounded-2xl p-8" style={{ height: '70vh' }}>
            <AnimatePresence mode="wait">
              {!selectedGame ? (
                <motion.div
                  key="console"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                  className="w-full flex items-center justify-center"
                >
                  <GameConsole
                    games={games}
                    onGameSelect={handleGameSelect}
                    isTransitioning={isTransitioning}
                  />
                </motion.div>
              ) : (
                <motion.div
                  key="game-screen"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                  className="w-full flex items-center justify-center"
                >
                  <GameScreen
                    game={selectedGame}
                    onBack={handleBackToConsole}
                    isTransitioning={isTransitioning}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Bonus Discovery Arrow - Points to contact section */}
        <div className="flex justify-center mt-12">
          <div className="text-center">
            <p className="text-secondary/60 text-sm mb-4 font-medium">
              Ready to work together? Let's connect! 👇
            </p>
            <div className="scroll-down-arrow">
              <svg
                className="w-6 h-6 text-accent mx-auto"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 14l-7 7m0 0l-7-7m7 7V3"
                />
              </svg>
            </div>
          </div>
        </div>

      </div>
    </section>
  );
};

export default RetroGamesSection;
